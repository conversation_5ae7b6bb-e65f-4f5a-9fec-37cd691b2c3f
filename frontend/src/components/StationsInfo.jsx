import { useState, useEffect } from 'react'
import axios from 'axios'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-leaflet'
import 'leaflet/dist/leaflet.css'
import L from 'leaflet'
import './StationsInfo.css'

// Fix for default markers in react-leaflet
delete L.Icon.Default.prototype._getIconUrl
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
})

function StationsInfo() {
  const [countries, setCountries] = useState([])
  const [selectedCountry, setSelectedCountry] = useState('')
  const [stationCount, setStationCount] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [activeTab, setActiveTab] = useState('list')
  const [mapData, setMapData] = useState([])

  useEffect(() => {
    fetchCountries()
    fetchAllStationsData()
  }, [])

  // Coordonnées approximatives des pays (centres géographiques)
  const countryCoordinates = {
    'FR': [46.603354, 1.888334],
    'ES': [40.463667, -3.74922],
    'US': [39.50, -98.35],
    'DE': [51.165691, 10.451526],
    'GB': [55.378051, -3.435973],
    'IT': [41.87194, 12.56738],
    'CA': [56.130366, -106.346771],
    'AU': [-25.274398, 133.775136],
    'NL': [52.132633, 5.291266],
    'BE': [50.503887, 4.469936],
    'AT': [47.516231, 14.550072],
    'CH': [46.818188, 8.227512],
    'NO': [60.472024, 8.468946],
    'SE': [60.128161, 18.643501],
    'DK': [56.26392, 9.501785],
    'PL': [51.919438, 19.145136],
    'CZ': [49.817492, 15.472962],
    'PT': [39.399872, -8.224454],
    'IE': [53.41291, -8.24389],
    'FI': [61.92411, 25.748151],
    'JP': [36.204824, 138.252924],
    'KR': [35.907757, 127.766922],
    'CN': [35.86166, 104.195397],
    'BR': [-14.235004, -51.92528],
    'MX': [23.634501, -102.552784],
    'AR': [-38.416097, -63.616672],
    'CL': [-35.675147, -71.542969],
    'CO': [4.570868, -74.297333],
    'TR': [38.963745, 35.243322],
    'RU': [61.52401, 105.318756],
    'IN': [20.593684, 78.96288],
    'TH': [15.870032, 100.992541],
    'SG': [1.352083, 103.819836],
    'MY': [4.210484, 101.975766],
    'ID': [-0.789275, 113.921327],
    'PH': [12.879721, 121.774017],
    'VN': [14.058324, 108.277199],
    'TW': [23.69781, 120.960515],
    'NZ': [-40.900557, 174.885971],
    'ZA': [-30.559482, 22.937506],
    'EG': [26.820553, 30.802498],
    'MA': [31.791702, -7.09262],
    'IL': [31.046051, 34.851612],
    'AE': [23.424076, 53.847818],
    'SA': [23.885942, 45.079162],
    'SI': [46.151241, 14.995463],
    'HR': [45.1, 15.2],
    'HU': [47.162494, 19.503304],
    'SK': [48.669026, 19.699024],
    'LT': [55.169438, 23.881275],
    'LV': [56.879635, 24.603189],
    'EE': [58.595272, 25.013607],
    'LU': [49.815273, 6.129583],
    'MT': [35.937496, 14.375416],
    'CY': [35.126413, 33.429859],
    'IS': [64.963051, -19.020835],
    'AD': [42.546245, 1.601554],
    'MC': [43.750298, 7.412841],
    'SM': [43.94236, 12.457777],
    'LI': [47.166, 9.555373],
    'VA': [41.902916, 12.453389]
  }

  const fetchCountries = async () => {
    try {
      const response = await axios.get('http://127.0.0.1:5000/networks')
      const uniqueCountries = [...new Set(response.data.map(item => item.country))].sort()
      setCountries(uniqueCountries)
    } catch (err) {
      console.error('Erreur lors du chargement des pays:', err)
      setError('Erreur lors du chargement des pays')
    }
  }

  const fetchAllStationsData = async () => {
    try {
      setLoading(true)
      const response = await axios.get('http://127.0.0.1:5000/networks')
      // Traitement par lots de 50 éléments pour éviter la surcharge
      const batchSize = 50;
      const processedData = [];
      
      for (let i = 0; i < response.data.length; i += batchSize) {
        const batch = response.data.slice(i, i + batchSize);
        const batchWithCoords = batch.map(item => ({
          ...item,
          coordinates: countryCoordinates[item.country] || [0, 0]
        }));
        processedData.push(...batchWithCoords);
        
        // Petite pause entre les lots pour laisser respirer le navigateur
        if (i + batchSize < response.data.length) {
          await new Promise(resolve => setTimeout(resolve, 10));
        }
      }
      
      setMapData(processedData);
    } catch (err) {
      console.error('Erreur lors du chargement des données de la carte:', err)
    } finally {
      setLoading(false)
    }
  }

  const fetchStationCount = async (country) => {
    if (!country) return

    try {
      setLoading(true)
      setError(null)
      const response = await axios.get(`http://127.0.0.1:5000/stations?country=${encodeURIComponent(country)}`)
      setStationCount(response.data.total_stations)
    } catch (err) {
      setError('Erreur lors du chargement du nombre de stations')
      console.error('Erreur:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleCountrySelect = (country) => {
    setSelectedCountry(country)
    setStationCount(null)
    fetchStationCount(country)
  }

  const renderMapView = () => (
    <div className="map-container">
      <MapContainer
        center={[20, 0]}
        zoom={2}
        style={{ height: '500px', width: '100%' }}
        className="stations-map"
      >
        <TileLayer
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        />
        {mapData.map((country, index) => {
          const [lat, lng] = country.coordinates
          if (lat === 0 && lng === 0) return null

          return (
            <Marker key={index} position={[lat, lng]}>
              <Tooltip permanent={false} direction="top">
                <div className="map-tooltip">
                  <strong>{country.country}</strong><br/>
                  {country.stations} {country.stations === 1 ? 'station' : 'stations'}
                </div>
              </Tooltip>
              <Popup>
                <div className="map-popup">
                  <h4>{country.country}</h4>
                  <p><strong>{country.stations}</strong> {country.stations === 1 ? 'station' : 'stations'}</p>
                </div>
              </Popup>
            </Marker>
          )
        })}
      </MapContainer>
    </div>
  )

  return (
    <div className="stations-info">
      <div className="page-header">
        <h2>📊 Informations sur les Stations</h2>
        <p>Explorez les stations par pays avec la liste ou la carte interactive</p>
      </div>

      {/* Onglets */}
      <div className="tabs-container">
        <div className="tabs">
          <button
            className={`tab ${activeTab === 'list' ? 'active' : ''}`}
            onClick={() => setActiveTab('list')}
          >
            <span className="tab-icon">📋</span>
            <span>Liste</span>
          </button>
          <button
            className={`tab ${activeTab === 'map' ? 'active' : ''}`}
            onClick={() => setActiveTab('map')}
          >
            <span className="tab-icon">🗺️</span>
            <span>Carte</span>
          </button>
        </div>
      </div>

      {/* Contenu selon l'onglet actif */}
      {activeTab === 'list' && (
        <div className="content-grid">
          <div className="countries-section">
            <h3>Choisir un pays</h3>
            {error && !selectedCountry && (
              <div className="error">
                <div className="icon">⚠️</div>
                <p>{error}</p>
              </div>
            )}
            <div className="countries-list">
              {countries.map((country) => (
                <div
                  key={country}
                  className={`country-item ${selectedCountry === country ? 'selected' : ''}`}
                  onClick={() => handleCountrySelect(country)}
                >
                  <span className="country-name">{country}</span>
                </div>
              ))}
            </div>
          </div>

          <div className="station-details">
            {!selectedCountry && (
              <div className="no-selection">
                <div className="icon">🗺️</div>
                <h3>Sélectionnez un pays</h3>
                <p>Choisissez un pays dans la liste pour voir le nombre de stations</p>
              </div>
            )}

            {selectedCountry && loading && (
              <div className="loading">
                <div className="spinner"></div>
                <p>Chargement du nombre de stations...</p>
              </div>
            )}

            {selectedCountry && error && (
              <div className="error">
                <div className="icon">⚠️</div>
                <p>{error}</p>
              </div>
            )}

            {selectedCountry && stationCount !== null && !loading && !error && (
              <div className="station-info-card">
                <div className="card-header">
                  <div className="country-flag">🌍</div>
                  <h3>{selectedCountry}</h3>
                  <div className="total-stations">
                    <span className="number">{stationCount.toLocaleString()}</span>
                    <span className="label">{stationCount === 1 ? 'Station' : 'Stations'}</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Onglet Carte */}
      {activeTab === 'map' && (
        <div className="map-view">
          <div className="map-header">
            <h3>🗺️ Carte des Stations par Pays</h3>
            <p>Survolez les marqueurs pour voir le nombre de stations</p>
          </div>
          {renderMapView()}
        </div>
      )}
    </div>
  )
}

export default StationsInfo
