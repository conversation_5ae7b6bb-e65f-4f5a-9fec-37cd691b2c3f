import { useState, useEffect } from 'react'
import axios from 'axios'
import './SearchableNetworks.css'

function SearchableNetworks() {
  const [networks, setNetworks] = useState([])
  const [filteredNetworks, setFilteredNetworks] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [countryFilter, setCountryFilter] = useState('')

  useEffect(() => {
    fetchNetworks()
  }, [])

  useEffect(() => {
    filterNetworks()
  }, [networks, countryFilter])

  const fetchNetworks = async () => {
    try {
      setLoading(true)
      const response = await axios.get('http://127.0.0.1:5000/networks')
      setNetworks(response.data)
      setError(null)
    } catch (err) {
      setError('Erreur lors du chargement des réseaux')
      console.error('Erreur:', err)
    } finally {
      setLoading(false)
    }
  }

  const filterNetworks = () => {
    if (!countryFilter.trim()) {
      setFilteredNetworks(networks)
    } else {
      const filtered = networks.filter(network =>
        network.country.toLowerCase().includes(countryFilter.toLowerCase())
      )
      setFilteredNetworks(filtered)
    }
  }

  const handleFilterChange = (e) => {
    setCountryFilter(e.target.value)
  }

  const clearFilter = () => {
    setCountryFilter('')
  }

  return (
    <div className="searchable-networks">
      <div className="page-header">
        <h2>🔍 Réseaux de Vélos</h2>
        <p>Recherchez et filtrez les réseaux par pays</p>
      </div>

      <div className="search-section">
        <div className="search-container">
          <div className="search-input-group">
            <input
              type="text"
              placeholder="Filtrer par pays (ex: France, Spain, US...)"
              value={countryFilter}
              onChange={handleFilterChange}
              className="search-input"
            />
            {countryFilter && (
              <button onClick={clearFilter} className="clear-btn">
                ✕
              </button>
            )}
          </div>
          <div className="search-info">
            {countryFilter && (
              <span className="filter-info">
                Filtré par: "{countryFilter}" - {filteredNetworks.length} résultat(s)
              </span>
            )}
          </div>
        </div>
      </div>

      {loading && <div className="loading">Chargement des réseaux...</div>}

      {error && <div className="error">{error}</div>}

      {!loading && !error && (
        <div className="networks-table-container">
          <div className="table-header">
            <h3>
              Réseaux ({filteredNetworks.length})
              {countryFilter && ` - Filtrés par "${countryFilter}"`}
            </h3>
          </div>

          {filteredNetworks.length === 0 ? (
            <div className="no-data">
              {countryFilter ? 
                `Aucun réseau trouvé pour "${countryFilter}"` : 
                'Aucun réseau disponible'
              }
            </div>
          ) : (
            <div className="table-wrapper">
              <table className="networks-table">
                <thead>
                  <tr>
                    <th>Nom</th>
                    <th>Entreprise</th>
                    <th>Ville</th>
                    <th>Pays</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredNetworks.map((network, index) => (
                    <tr key={network.id || index} className={index % 2 === 0 ? 'even' : 'odd'}>
                      <td className="network-name">{network.name}</td>
                      <td className="network-company">{network.company || 'N/A'}</td>
                      <td className="network-city">{network.city}</td>
                      <td className="network-country">{network.country}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default SearchableNetworks
