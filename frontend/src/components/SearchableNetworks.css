.searchable-networks {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.page-header {
  text-align: center;
  margin-bottom: 2rem;
}

.page-header h2 {
  color: #333;
  margin-bottom: 0.5rem;
  font-size: 2rem;
}

.page-header p {
  color: #666;
  font-size: 1.1rem;
}

.search-section {
  margin-bottom: 2rem;
}

.search-container {
  max-width: 600px;
  margin: 0 auto;
}

.search-input-group {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  width: 100%;
  padding: 1rem 1.5rem;
  font-size: 1rem;
  border: 2px solid #e1e5e9;
  border-radius: 50px;
  outline: none;
  transition: all 0.3s ease;
  background: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.search-input:focus {
  border-color: #667eea;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.2);
}

.clear-btn {
  position: absolute;
  right: 1rem;
  background: #ff6b6b;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  transition: all 0.2s ease;
}

.clear-btn:hover {
  background: #ff5252;
  transform: scale(1.1);
}

.search-info {
  margin-top: 0.5rem;
  text-align: center;
}

.filter-info {
  color: #667eea;
  font-weight: 500;
  font-size: 0.9rem;
}

.networks-table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.table-wrapper {
  display: block;
  overflow-y: auto;
  max-height: 400px;
}

.table-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem;
}

.table-header h3 {
  margin: 0;
  font-size: 1.3rem;
}

.table-wrapper {
  overflow-x: auto;
}

.networks-table {
  width: 100%;
  border-collapse: collapse;
}

.networks-table th {
  background: #f8f9fa;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #e9ecef;
  position: sticky;
  top: 0;
}

.networks-table td {
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;
  transition: background-color 0.2s ease;
}

.networks-table tr:hover td {
  background-color: #f8f9ff;
}

.networks-table tr.even {
  background-color: #fafafa;
}

.networks-table tr.odd {
  background-color: white;
}

.network-name {
  font-weight: 600;
  color: #333;
}

.network-company {
  color: #666;
  font-style: italic;
}

.network-city {
  color: #555;
}

.network-country {
  color: #667eea;
  font-weight: 500;
}

.loading, .error, .no-data {
  text-align: center;
  padding: 3rem;
  font-size: 1.1rem;
}

.loading {
  color: #667eea;
}

.error {
  color: #ff6b6b;
  background: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 8px;
}

.no-data {
  color: #666;
  background: #f7f7f7;
  border-radius: 8px;
}

@media (max-width: 768px) {
  .searchable-networks {
    padding: 0 0.5rem;
  }
  
  .page-header h2 {
    font-size: 1.5rem;
  }
  
  .search-input {
    padding: 0.8rem 1rem;
    font-size: 0.9rem;
  }
  
  .networks-table th,
  .networks-table td {
    padding: 0.75rem 0.5rem;
    font-size: 0.9rem;
  }
  
  .table-header {
    padding: 1rem;
  }
  
  .table-header h3 {
    font-size: 1.1rem;
  }
}
