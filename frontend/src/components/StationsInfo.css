.stations-info {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.page-header {
  text-align: center;
  margin-bottom: 2rem;
}

.page-header h2 {
  color: #333;
  margin-bottom: 0.5rem;
  font-size: 2rem;
}

.page-header p {
  color: #666;
  font-size: 1.1rem;
}

.content-grid {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 2rem;
  margin-bottom: 3rem;
}

.countries-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  height: fit-content;
}

.countries-section h3 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.2rem;
}

.countries-list {
  height: 500px;
  overflow-y: scroll;
}

.country-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  margin-bottom: 0.5rem;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.country-item:hover {
  border-color: #667eea;
  background: #f8f9ff;
  transform: translateX(4px);
}

.country-item.selected {
  border-color: #667eea;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: translateX(4px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.country-name {
  font-weight: 500;
  font-size: 1rem;
}

.station-count {
  font-size: 0.9rem;
  opacity: 0.8;
  font-weight: 600;
}

.station-details {
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 3rem;
  min-height: 450px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.no-selection {
  text-align: center;
  color: #666;
}

.no-selection .icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.no-selection h3 {
  margin: 0 0 0.5rem 0;
  color: #333;
}

.loading {
  text-align: center;
  color: #667eea;
  padding: 2rem;
}

.spinner {
  width: 60px;
  height: 60px;
  border: 6px solid rgba(102, 126, 234, 0.1);
  border-top: 6px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1.5rem;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
}

.loading p {
  font-size: 1.1rem;
  font-weight: 500;
  color: #667eea;
  margin: 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error {
  text-align: center;
  color: #ff6b6b;
}

.error .icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.station-info-card {
  width: 100%;
  align-self: flex-start;
  text-align: center;
}

.card-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
  padding: 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  color: white;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.card-header::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { transform: rotate(0deg); }
  50% { transform: rotate(180deg); }
}

.card-header h3 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0,0,0,0.2);
  z-index: 1;
  position: relative;
}

.total-stations {
  text-align: center;
  z-index: 1;
  position: relative;
}

.total-stations .number {
  display: block;
  font-size: 4rem;
  font-weight: 900;
  color: white;
  line-height: 1;
  text-shadow: 0 4px 8px rgba(0,0,0,0.3);
  margin-bottom: 0.5rem;
  animation: countUp 0.8s ease-out;
}

@keyframes countUp {
  from {
    opacity: 0;
    transform: scale(0.5) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.total-stations .label {
  font-size: 1.1rem;
  color: rgba(255,255,255,0.9);
  text-transform: uppercase;
  letter-spacing: 2px;
  font-weight: 500;
}

.country-flag {
  font-size: 3rem;
  margin-bottom: 1rem;
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
}

.networks-list h4 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.1rem;
  
}

.networks-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  max-height: 400px;
  overflow-y: auto;
}

.network-item {
  background: #f8f9ff;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1rem;
  transition: all 0.2s ease;
}

/* Styles pour les onglets */
.tabs-container {
  margin: 2rem 0;
  display: flex;
  justify-content: center;
}

.tabs {
  display: flex;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 0.5rem;
  gap: 0.5rem;
}

.tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border: none;
  background: transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  font-weight: 500;
  color: #666;
}

.tab:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.tab.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.tab-icon {
  font-size: 1.2rem;
}

/* Styles pour la vue carte */
.map-view {
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-bottom: 2rem;
}

.map-header {
  text-align: center;
  margin-bottom: 2rem;
}

.map-header h3 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1.5rem;
}

.map-header p {
  margin: 0;
  color: #666;
  font-size: 1rem;
}

.map-container {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.stations-map {
  border-radius: 12px;
}

/* Styles pour les tooltips et popups de la carte */
.map-tooltip {
  text-align: center;
  font-size: 0.9rem;
  padding: 0.25rem;
}

.map-popup h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1.1rem;
}

.map-popup p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

/* Styles responsifs pour les onglets */
@media (max-width: 768px) {
  .tabs {
    flex-direction: column;
    width: 100%;
    max-width: 300px;
  }

  .tab {
    justify-content: center;
    padding: 0.75rem 1.5rem;
  }

  .map-view {
    padding: 1rem;
  }

  .map-container {
    height: 400px;
  }

  .stations-map {
    height: 400px !important;
  }
}

/* Animation pour le changement d'onglets */
.content-grid,
.map-view {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Amélioration des marqueurs sur la carte */
.leaflet-marker-icon {
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
}

.leaflet-popup-content-wrapper {
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.leaflet-tooltip {
  background: rgba(0, 0, 0, 0.8) !important;
  border: none !important;
  border-radius: 6px !important;
  color: white !important;
  font-weight: 500;
}

.network-item:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.network-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.25rem;
}

.network-city {
  color: #666;
  font-size: 0.9rem;
}

.no-networks {
  color: #666;
  font-style: italic;
  text-align: center;
  padding: 2rem;
}

.summary-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

.summary-section h3 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.3rem;
  text-align: center;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  text-align: center;
  padding: 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.stat-number {
  font-size: 2rem;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.9;
  text-transform: uppercase;
  letter-spacing: 1px;
}

@media (max-width: 768px) {
  .content-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .station-details {
    min-height: 300px;
    padding: 1.5rem;
  }

  .card-header {
    padding: 1.5rem;
    gap: 1.5rem;
  }

  .card-header h3 {
    font-size: 1.5rem;
  }

  .total-stations .number {
    font-size: 3rem;
  }

  .total-stations .label {
    font-size: 1rem;
  }

  .country-flag {
    font-size: 2.5rem;
  }

  .networks-grid {
    grid-template-columns: 1fr;
  }

  .summary-stats {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}
