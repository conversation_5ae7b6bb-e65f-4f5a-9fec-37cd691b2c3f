/* Variables CSS pour la cohérence */
:root {
  --primary-color: #2563eb;
  --secondary-color: #64748b;
  --success-color: #059669;
  --danger-color: #dc2626;
  --warning-color: #d97706;
  --background-color: #f8fafc;
  --card-background: #ffffff;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --border-color: #e2e8f0;
  --border-radius: 8px;
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Reset et styles de base */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> U<PERSON>', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', '<PERSON>ra Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--background-color);
  color: var(--text-primary);
  line-height: 1.6;
}

.app {
  min-height: 100vh;
}

/* Header */
.app-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, #3b82f6 100%);
  color: white;
  padding: 2rem 1rem;
  text-align: center;
  box-shadow: var(--shadow-lg);
}

.app-header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2.5rem;
  font-weight: 700;
}

.app-header p {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

/* Contenu principal */
.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

/* Actions */
.actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

/* Boutons */
.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--border-radius);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn:active {
  transform: translateY(0);
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: #1d4ed8;
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-secondary:hover {
  background-color: #475569;
}

.btn-success {
  background-color: var(--success-color);
  color: white;
}

.btn-success:hover {
  background-color: #047857;
}

.btn-cancel {
  background-color: var(--danger-color);
  color: white;
}

.btn-cancel:hover {
  background-color: #b91c1c;
}

/* Conteneur de formulaire */
.form-container {
  background: var(--card-background);
  border-radius: var(--border-radius);
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: var(--shadow);
  border: 1px solid var(--border-color);
}

.form-container h2 {
  margin: 0 0 1.5rem 0;
  color: var(--text-primary);
  font-size: 1.5rem;
}

/* Formulaire */
.network-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group label {
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.form-group input {
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-group input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1rem;
}

/* Section des réseaux */
.networks-section h2 {
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  font-size: 1.8rem;
}

/* États de chargement et d'erreur */
.loading, .error, .no-data {
  text-align: center;
  padding: 3rem 1rem;
  font-size: 1.1rem;
}

.loading {
  color: var(--text-secondary);
}

.error {
  color: var(--danger-color);
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: var(--border-radius);
}

.no-data {
  color: var(--text-secondary);
  background-color: #f8fafc;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
}

/* Grille des réseaux */
.networks-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

/* Cartes de réseau */
.network-card {
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: var(--shadow);
  transition: all 0.2s ease;
}

.network-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.network-card h3 {
  margin: 0 0 1rem 0;
  color: var(--primary-color);
  font-size: 1.3rem;
  font-weight: 600;
}

.network-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.network-info p {
  margin: 0;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.network-info strong {
  color: var(--text-primary);
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-header {
    padding: 1.5rem 1rem;
  }

  .app-header h1 {
    font-size: 2rem;
  }

  .main-content {
    padding: 1rem;
  }

  .actions {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }

  .form-container {
    padding: 1.5rem;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
  }

  .networks-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .network-card {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .app-header h1 {
    font-size: 1.8rem;
  }

  .app-header p {
    font-size: 1rem;
  }

  .form-container {
    padding: 1rem;
  }

  .network-card {
    padding: 1rem;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.network-card {
  animation: fadeIn 0.3s ease-out;
}

.form-container {
  animation: fadeIn 0.3s ease-out;
}

/* Focus et accessibilité */
.btn:focus,
.form-group input:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Amélioration de l'UX */
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn:disabled:hover {
  transform: none;
  box-shadow: var(--shadow);
}
