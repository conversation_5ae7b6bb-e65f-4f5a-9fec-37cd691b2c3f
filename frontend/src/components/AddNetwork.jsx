import { useState } from 'react'
import axios from 'axios'
import './AddNetwork.css'

function AddNetwork() {
  const [formData, setFormData] = useState({
    name: '',
    company: '',
    city: '',
    country: '',
  })
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState(null)
  const [messageType, setMessageType] = useState('') // 'success' or 'error'

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const resetForm = () => {
    setFormData({
      name: '',
      company: '',
      city: '',
      country: '',
    })
  }

  const showMessage = (text, type) => {
    setMessage(text)
    setMessageType(type)
    setTimeout(() => {
      setMessage(null)
      setMessageType('')
    }, 5000)
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    // Validation
    if (!formData.name || !formData.city || !formData.country) {
      showMessage('Veuillez remplir tous les champs obligatoires', 'error')
      return
    }

    try {
      setLoading(true)
      
      const networkData = {
        name: formData.name,
        company: formData.company || '',
        city: formData.city,
        country: formData.country,
      }

      // Call POST endpoint to create network
      await axios.post('http://127.0.0.1:5000/networks', networkData)

      showMessage('Réseau ajouté avec succès!', 'success')
      resetForm()

    } catch (err) {
      console.error('Erreur:', err)
      const errorMessage = err.response?.data?.error || 'Erreur lors de l\'ajout du réseau'
      showMessage(errorMessage, 'error')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="add-network">
      <div className="page-header">
        <h2>➕ Ajouter un Réseau</h2>
        <p>Créez un nouveau réseau de vélos en libre-service</p>
      </div>

      <div className="form-container">
        <form onSubmit={handleSubmit} className="network-form">
          <div className="form-grid">
            <div className="form-group">
              <label htmlFor="name" className="required">
                Nom du réseau
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="Ex: Vélib', Bicing, Citi Bike..."
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="company">
                Entreprise
              </label>
              <input
                type="text"
                id="company"
                name="company"
                value={formData.company}
                onChange={handleInputChange}
                placeholder="Ex: JCDecaux, Clear Channel..."
              />
            </div>

            <div className="form-group">
              <label htmlFor="city" className="required">
                Ville
              </label>
              <input
                type="text"
                id="city"
                name="city"
                value={formData.city}
                onChange={handleInputChange}
                placeholder="Ex: Paris, Barcelona, New York..."
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="country" className="required">
                Pays
              </label>
              <input
                type="text"
                id="country"
                name="country"
                value={formData.country}
                onChange={handleInputChange}
                placeholder="Ex: France, Spain, United States..."
                required
              />
            </div>
          </div>

          {message && (
            <div className={`message ${messageType}`}>
              <div className="message-icon">
                {messageType === 'success' ? '✅' : '⚠️'}
              </div>
              <span>{message}</span>
            </div>
          )}

          <div className="form-actions">
            <button 
              type="submit" 
              className="btn btn-primary"
              disabled={loading}
            >
              {loading ? (
                <>
                  <span className="spinner-small"></span>
                  Ajout en cours...
                </>
              ) : (
                'Ajouter le réseau'
              )}
            </button>
            
            <button
              type="button"
              className="btn btn-secondary"
              onClick={resetForm}
              disabled={loading}
            >
              Réinitialiser
            </button>
          </div>
        </form>

        <div className="form-info">
          <h3>ℹ️ Informations</h3>
          <ul>
            <li>Les champs marqués d'un * sont obligatoires</li>
            <li>Le réseau sera ajouté à la base de données</li>
            <li>Vous pourrez voir le nouveau réseau dans la liste des réseaux</li>
            <li>Utilisé un code pays a la place du nom complet</li>
          </ul>
        </div>
      </div>
    </div>
  )
}

export default AddNetwork
