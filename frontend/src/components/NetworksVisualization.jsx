import { useState, useEffect } from 'react'
import axios from 'axios'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement,
} from 'chart.js'
import { <PERSON>, Doughnut, Line } from 'react-chartjs-2'
import './NetworksVisualization.css'

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement
)

function NetworksVisualization() {
  const [networks, setNetworks] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [activeChart, setActiveChart] = useState('countries')

  useEffect(() => {
    fetchNetworks()
  }, [])

  const fetchNetworks = async () => {
    try {
      setLoading(true)
      const response = await axios.get('http://127.0.0.1:5000/networks')
      setNetworks(response.data)
      setError(null)
    } catch (err) {
      setError('Erreur lors du chargement des données')
      console.error('Erreur:', err)
    } finally {
      setLoading(false)
    }
  }

  // Data processing functions
  const getCountryDistribution = () => {
    const countryCount = {}
    networks.forEach(network => {
      countryCount[network.country] = (countryCount[network.country] || 0) + 1
    })
    
    const sortedCountries = Object.entries(countryCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10) // Top 10 countries
    
    return {
      labels: sortedCountries.map(([country]) => country),
      datasets: [{
        label: 'Nombre de réseaux',
        data: sortedCountries.map(([, count]) => count),
        backgroundColor: [
          '#667eea', '#764ba2', '#f093fb', '#f5576c',
          '#4facfe', '#00f2fe', '#43e97b', '#38f9d7',
          '#ffecd2', '#fcb69f'
        ],
        borderColor: '#fff',
        borderWidth: 2,
      }]
    }
  }

  const getCityDistribution = () => {
    const cityCount = {}
    networks.forEach(network => {
      const city = network.city?.trim().toLowerCase() // Nettoyer et convertir en minuscules
      if (city) { // Vérifier que la ville n'est pas vide
        cityCount[city] = (cityCount[city] || 0) + 1
      }
    })
    
    console.log("cityCount", cityCount)
    const sortedCities = Object.entries(cityCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 15) // Top 15 cities
    
    return {
      labels: sortedCities.map(([city]) => city),
      datasets: [{
        label: 'Nombre de réseaux',
        data: sortedCities.map(([, count]) => count),
        backgroundColor: 'rgba(102, 126, 234, 0.8)',
        borderColor: '#667eea',
        borderWidth: 2,
      }]
    }
  }

  const getCompanyDistribution = () => {
    const companyCount = {}
    networks.forEach(network => {
      const company = network.company || 'Non spécifié'
      companyCount[company] = (companyCount[company] || 0) + 1
    })
    
    const sortedCompanies = Object.entries(companyCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 8) // Top 8 companies
    
    return {
      labels: sortedCompanies.map(([company]) => company),
      datasets: [{
        data: sortedCompanies.map(([, count]) => count),
        backgroundColor: [
          '#667eea', '#764ba2', '#f093fb', '#f5576c',
          '#4facfe', '#00f2fe', '#43e97b', '#38f9d7'
        ],
        borderColor: '#fff',
        borderWidth: 3,
      }]
    }
  }

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Distribution des Réseaux de Vélos',
        font: {
          size: 16,
          weight: 'bold'
        }
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          stepSize: 1
        }
      }
    }
  }

  const doughnutOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right',
      },
      title: {
        display: true,
        text: 'Répartition par Entreprise',
        font: {
          size: 16,
          weight: 'bold'
        }
      },
    },
  }

  const chartTypes = [
    { id: 'countries', label: 'Par Pays', icon: '🌍' },
    { id: 'cities', label: 'Par Ville', icon: '🏙️' },
    { id: 'companies', label: 'Par Entreprise', icon: '🏢' }
  ]

  if (loading) {
    return (
      <div className="visualization-loading">
        <div className="spinner"></div>
        <p>Chargement des données de visualisation...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="visualization-error">
        <div className="error-icon">⚠️</div>
        <p>{error}</p>
        <button onClick={fetchNetworks} className="retry-btn">
          Réessayer
        </button>
      </div>
    )
  }

  return (
    <div className="networks-visualization">
      <div className="page-header">
        <h2>📈 Visualisation des Réseaux</h2>
        <p>Analysez la distribution des réseaux de vélos à travers le monde</p>
      </div>

      <div className="chart-controls">
        {chartTypes.map(type => (
          <button
            key={type.id}
            className={`chart-btn ${activeChart === type.id ? 'active' : ''}`}
            onClick={() => setActiveChart(type.id)}
          >
            <span className="chart-icon">{type.icon}</span>
            <span>{type.label}</span>
          </button>
        ))}
      </div>

      <div className="charts-container">
        {activeChart === 'countries' && (
          <div className="chart-wrapper">
            <Bar data={getCountryDistribution()} options={chartOptions} />
          </div>
        )}

        {activeChart === 'cities' && (
          <div className="chart-wrapper">
            <Bar data={getCityDistribution()} options={chartOptions} />
          </div>
        )}

        {activeChart === 'companies' && (
          <div className="chart-wrapper">
            <Doughnut data={getCompanyDistribution()} options={doughnutOptions} />
          </div>
        )}
      </div>

      <div className="stats-summary">
        <div className="stat-card">
          <div className="stat-number">{networks.length}</div>
          <div className="stat-label">Total Réseaux</div>
        </div>
        <div className="stat-card">
          <div className="stat-number">
            {new Set(networks.map(n => n.country)).size}
          </div>
          <div className="stat-label">Pays</div>
        </div>
        <div className="stat-card">
          <div className="stat-number">
            {new Set(networks.map(n => n.city)).size}
          </div>
          <div className="stat-label">Villes</div>
        </div>
        <div className="stat-card">
          <div className="stat-number">
            {new Set(networks.map(n => n.company).filter(Boolean)).size}
          </div>
          <div className="stat-label">Entreprises</div>
        </div>
      </div>
    </div>
  )
}

export default NetworksVisualization
